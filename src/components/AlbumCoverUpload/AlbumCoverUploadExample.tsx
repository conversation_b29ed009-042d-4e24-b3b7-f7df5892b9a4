import React, { useState } from 'react';
import { Card, Space, Typography } from 'antd';
import AlbumCoverUpload from './AlbumCoverUpload';

const { Title, Text } = Typography;

const AlbumCoverUploadExample: React.FC = () => {
  const [coverUrl, setCoverUrl] = useState<string>('');

  // 处理上传成功
  const handleUploadSuccess = (url: string) => {
    setCoverUrl(url);
    console.log('专辑封面上传成功:', url);
  };

  // 自定义上传请求（示例）
  const customUploadRequest = async (options: any) => {
    const { file, onSuccess, onError } = options;
    
    try {
      // 这里应该调用你的上传 API
      // const response = await api.album.uploadCover(file);
      // onSuccess({ url: response.url });
      
      // 示例：使用本地 URL
      const url = URL.createObjectURL(file);
      onSuccess({ url });
    } catch (error) {
      console.error('上传失败:', error);
      onError(error);
    }
  };

  return (
    <div className="p-6">
      <Card title="专辑封面上传组件示例" className="max-w-2xl mx-auto">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={4}>基础用法</Title>
            <Text type="secondary">
              支持图片裁切，方形比例，适合专辑封面
            </Text>
          </div>
          
          <div className="flex justify-center">
            <AlbumCoverUpload
              coverUrl={coverUrl}
              size={200}
              onUploadSuccess={handleUploadSuccess}
              customRequest={customUploadRequest}
            />
          </div>
          
          {coverUrl && (
            <div>
              <Title level={5}>当前封面 URL:</Title>
              <Text code>{coverUrl}</Text>
            </div>
          )}
          
          <div>
            <Title level={4}>不同尺寸示例</Title>
            <Space size="large">
              <div className="text-center">
                <AlbumCoverUpload size={120} onUploadSuccess={handleUploadSuccess} />
                <div className="mt-2">小尺寸 (120px)</div>
              </div>
              <div className="text-center">
                <AlbumCoverUpload size={160} onUploadSuccess={handleUploadSuccess} />
                <div className="mt-2">中尺寸 (160px)</div>
              </div>
              <div className="text-center">
                <AlbumCoverUpload size={240} onUploadSuccess={handleUploadSuccess} />
                <div className="mt-2">大尺寸 (240px)</div>
              </div>
            </Space>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default AlbumCoverUploadExample;
